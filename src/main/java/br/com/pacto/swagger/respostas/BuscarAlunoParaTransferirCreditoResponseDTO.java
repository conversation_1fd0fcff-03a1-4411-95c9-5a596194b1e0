package br.com.pacto.swagger.respostas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para representar a resposta da busca de aluno para transferência de crédito")
public class BuscarAlunoParaTransferirCreditoResponseDTO {
    
    @ApiModelProperty(value = "Código identificador único do aluno encontrado no sistema. Este código é utilizado para referenciar o aluno em outras operações de transferência de crédito.",
                     example = "12345")
    private Integer codigoAluno;
    
    @ApiModelProperty(value = "Nome completo do aluno encontrado no sistema. Este é o nome cadastrado no perfil do aluno.",
                     example = "João <PERSON>")
    private String nomeAluno;

    public Integer getCodigoAluno() {
        return codigoAluno;
    }

    public void setCodigoAluno(Integer codigoAluno) {
        this.codigoAluno = codigoAluno;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }
}
